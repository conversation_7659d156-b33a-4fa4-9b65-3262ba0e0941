"""
Database operations module for patient data and keyword storage.

This module handles all database interactions for the patient keyword pipeline:
- Connection management with SQL Server
- Patient data retrieval from clinical tables
- Hash-based change detection for efficient processing
- Storage of generated keywords and reasoning

The module uses pyodbc for SQL Server connectivity and pandas for data manipulation.
All database operations are designed to be transaction-safe and efficient.
"""

import pyodbc
import pandas as pd
from contextlib import contextmanager
from typing import Any, Dict, List, Tuple
import logging
import config
import json

logger = logging.getLogger("patient_pipeline")

# SQL statements for database operations
CREATE_TABLE_SQL = """
-- Create PatientKeywords table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PatientKeywords' AND xtype='U')
    CREATE TABLE PatientKeywords (
        id INT IDENTITY(1,1) PRIMARY KEY,           -- Auto-incrementing primary key
        patient_id NVARCHAR(50) NOT NULL,           -- Patient identifier
        keywords NVARCHAR(MAX),                     -- JSON array of selected keywords
        reasoning NVARCHAR(MAX),                    -- JSON object with keyword reasoning
        data_hash NVARCHAR(64),                     -- SHA-256 hash for change detection
        processed_datetime DATETIME DEFAULT GETDATE(), -- Timestamp of processing
        UNIQUE(patient_id)                          -- Ensure one record per patient
    );
-- Add data_hash column to existing table if missing (for schema migration)
IF COL_LENGTH('PatientKeywords','data_hash') IS NULL
    ALTER TABLE PatientKeywords ADD data_hash NVARCHAR(64);
"""

MERGE_SQL = """
-- Upsert operation: insert new records or update existing ones
MERGE PatientKeywords AS tgt
USING (VALUES (?, ?, ?, ?))    -- Parameters: patient_id, keywords, reasoning, data_hash
       AS src (patient_id, keywords, reasoning, data_hash)
ON tgt.patient_id = src.patient_id  -- Match on patient ID
WHEN MATCHED THEN UPDATE            -- Update existing record
     SET tgt.keywords = src.keywords,
         tgt.reasoning = src.reasoning,
         tgt.data_hash = src.data_hash,
         tgt.processed_datetime = GETDATE()  -- Update timestamp
WHEN NOT MATCHED THEN INSERT (patient_id, keywords, reasoning, data_hash)  -- Insert new record
     VALUES (src.patient_id, src.keywords, src.reasoning, src.data_hash);
"""

def _require(var: str, name: str) -> str:
    """
    Validate that a required configuration variable is not empty.

    Args:
        var (str): The configuration variable value to check
        name (str): The name of the configuration variable for error messages

    Returns:
        str: The validated configuration variable value

    Raises:
        EnvironmentError: If the configuration variable is empty or None
    """
    if not var:
        raise EnvironmentError(f"Missing required config: {name}")
    return var

@contextmanager
def sql_connection():
    """
    Context manager for SQL Server database connections.

    Creates a connection to the SQL Server database using configuration
    settings and ensures proper cleanup when the connection is no longer needed.
    Uses Windows authentication (Trusted_Connection=yes).

    Yields:
        pyodbc.Connection: Active database connection

    Raises:
        pyodbc.Error: If database connection fails
        EnvironmentError: If required configuration is missing

    Example:
        >>> with sql_connection() as conn:
        ...     cursor = conn.cursor()
        ...     cursor.execute("SELECT * FROM PatientKeywords")
    """
    conn_str = (
        f"DRIVER={{{config.DB_DRIVER}}};"
        f"SERVER={_require(config.DB_SERVER, 'DB_SERVER')};"
        f"DATABASE={config.DB_NAME};"
        f"Trusted_Connection=yes;"
        f"Encrypt={config.DB_ENCRYPT};"
        f"TrustServerCertificate={config.DB_TRUST_CERT};"
    )
    conn = pyodbc.connect(conn_str, timeout=15)
    try:
        yield conn
    finally:
        conn.close()

def ensure_tables(conn: pyodbc.Connection) -> None:
    """
    Ensure that required database tables exist, creating them if necessary.

    Creates the PatientKeywords table if it doesn't exist and adds the data_hash
    column if it's missing from an existing table. This function is idempotent
    and safe to call multiple times.

    Args:
        conn (pyodbc.Connection): Active database connection

    Raises:
        pyodbc.Error: If table creation or modification fails
    """
    with conn.cursor() as cur:
        cur.execute(CREATE_TABLE_SQL)
        conn.commit()

def fetch_patient_ids(conn: pyodbc.Connection, offset: int, batch: int) -> List[str]:
    """
    Fetch a batch of patient IDs from the Appointment table with pagination.

    Retrieves distinct patient IDs from the Appointment table, optionally
    filtered by appointment date. Uses OFFSET/FETCH for efficient pagination
    through large datasets.

    Args:
        conn (pyodbc.Connection): Active database connection
        offset (int): Number of records to skip (for pagination)
        batch (int): Maximum number of patient IDs to return

    Returns:
        List[str]: List of patient IDs for this batch

    Example:
        >>> with sql_connection() as conn:
        ...     ids = fetch_patient_ids(conn, 0, 25)  # First 25 patients
        ...     ids = fetch_patient_ids(conn, 25, 25)  # Next 25 patients
    """
    logger.info(f"Fetching patient IDs from Appointment: offset={offset}, batch={batch}")

    # Base query for distinct patient IDs
    sql = """
        SELECT DISTINCT PatientID
        FROM dbo.Appointment
    """
    params = []

    # Add date filter if configured
    if config.APPOINTMENT_START_DATE:
        sql += " WHERE CAST(AppointmentDatetime AS DATE) >= ?"
        params.append(config.APPOINTMENT_START_DATE)

    # Add pagination
    sql += " ORDER BY PatientID OFFSET ? ROWS FETCH NEXT ? ROWS ONLY;"
    params.extend([offset, batch])

    # Execute query and return patient IDs
    ids = pd.read_sql(sql, conn, params=params)["PatientID"].tolist()
    logger.info(f"Fetched {len(ids)} patient IDs.")
    return ids

def fetch_patient_data(conn: pyodbc.Connection, patient_id: str) -> Dict[str, Any] | None:
    """
    Fetch comprehensive clinical data for a single patient.

    Retrieves and structures clinical data from multiple tables including:
    - Lab results and observations
    - Medical problems and diagnoses
    - Current medications
    - Known allergies

    The data is cleaned, deduplicated, and organized into a standardized format
    suitable for LLM processing.

    Args:
        conn (pyodbc.Connection): Active database connection
        patient_id (str): Unique patient identifier

    Returns:
        Dict[str, Any] | None: Structured patient data with keys:
            - Results: List of lab results and observations
            - Problems: List of medical problems/diagnoses
            - Medications: List of current medications
            - Allergies: List of known allergies
        Returns None if no clinical data found for the patient.

    Example:
        >>> with sql_connection() as conn:
        ...     data = fetch_patient_data(conn, "12345")
        ...     if data:
        ...         print(f"Patient has {len(data['Problems'])} problems")
    """
    # Complex JOIN query to gather all clinical data for the patient
    sql = """
    SELECT
        pr.PatientID,
        pr.ResultName, pr.ObservationValue, pr.ObservationUnit,
        pp.ProblemName, pp.Type, pp.DetailText,
        pm.MedType, pm.MedicationName, pm.DoseQuantity,
        pa.AllergyName
    FROM dbo.Patient_Result pr
    LEFT JOIN dbo.Patient_Problem    pp ON pr.PatientID = pp.PatientID
    LEFT JOIN dbo.Patient_Medication pm ON pr.PatientID = pm.PatientID
    LEFT JOIN dbo.Patient_Allergy    pa ON pr.PatientID = pa.PatientID
    WHERE pr.PatientID = ?;
    """
    df = pd.read_sql(sql, conn, params=[patient_id])

    # Return None if no clinical data found
    if df.empty:
        logger.warning(f"No clinical data found for patient_id={patient_id} (from Appointment table). Skipping.")
        return None
    # Map problem type codes to human-readable descriptions
    type_map = {
        "SocHx": "Social History",
        "PSH":   "Past Surgical History",
        "ROS":   "Review of Systems",
        "FamHx": "Family History",
        "PMH":   "Past Medical History",
    }

    # Extract and clean lab results/observations
    results = (
        df[df["ObservationValue"].notna() & (df["ObservationValue"].astype(str).str.strip() != "")]
        [["ResultName", "ObservationValue", "ObservationUnit"]]
        .drop_duplicates()
        .to_dict("records")
    )
    # Extract and structure medical problems/diagnoses
    problems: List[Dict[str, str]] = []
    for _, row in (
        df[["ProblemName", "Type", "DetailText"]]
        .drop_duplicates()
        .dropna(subset=["ProblemName"])
        .iterrows()
    ):
        p = {
            "ProblemName": row.ProblemName,
            "Type": type_map.get(row.Type, row.Type),  # Map type code to description
        }
        # Include detail text if available and non-empty
        if pd.notna(row.DetailText) and str(row.DetailText).strip():
            p["DetailText"] = row.DetailText
        problems.append(p)

    # Extract and clean medication data
    medications = (
        df[["MedType", "MedicationName", "DoseQuantity"]]
        .drop_duplicates()
        .dropna(subset=["MedicationName"])
        .to_dict("records")
    )

    # Extract allergy list
    allergies = df["AllergyName"].drop_duplicates().dropna().tolist()

    # Return structured patient data
    return {
        "Results": results,
        "Problems": problems,
        "Medications": medications,
        "Allergies": allergies,
    }

def fetch_batch_patient_data(conn: pyodbc.Connection, pids: List[str]) -> Dict[str, Any]:
    """
    Fetch clinical data for multiple patients in a single batch operation.

    Iterates through a list of patient IDs and fetches clinical data for each,
    handling errors gracefully by logging and skipping problematic patients.
    Only patients with valid clinical data are included in the result.

    Args:
        conn (pyodbc.Connection): Active database connection
        pids (List[str]): List of patient IDs to fetch data for

    Returns:
        Dict[str, Any]: Dictionary mapping patient IDs to their clinical data.
        Only includes patients with valid data.

    Example:
        >>> with sql_connection() as conn:
        ...     patient_ids = ["12345", "67890", "11111"]
        ...     batch_data = fetch_batch_patient_data(conn, patient_ids)
        ...     print(f"Successfully fetched data for {len(batch_data)} patients")
    """
    batch: Dict[str, Any] = {}
    for pid in pids:
        try:
            data = fetch_patient_data(conn, pid)
            if data:
                batch[str(pid)] = data
            else:
                logger.info(f"Patient {pid} has no data and will be skipped from processing batch.")
        except Exception:
            logger.exception("Failed to fetch data for patient_id=%s", pid)
    return batch

def get_existing_hashes(conn: pyodbc.Connection, pids: List[str]) -> Dict[str, str]:
    """
    Retrieve existing data hashes for a list of patient IDs.

    Fetches the stored data hashes for patients to enable change detection.
    If a patient's current data hash differs from the stored hash, the patient
    needs to be reprocessed. Returns empty dict if no patient IDs provided.

    Args:
        conn (pyodbc.Connection): Active database connection
        pids (List[str]): List of patient IDs to get hashes for

    Returns:
        Dict[str, str]: Dictionary mapping patient IDs to their stored data hashes.
        Only includes patients that have been processed before.

    Example:
        >>> with sql_connection() as conn:
        ...     patient_ids = ["12345", "67890"]
        ...     hashes = get_existing_hashes(conn, patient_ids)
        ...     print(f"Found existing hashes for {len(hashes)} patients")
    """
    if not pids:
        return {}

    # Build parameterized query to avoid SQL injection
    placeholders = ",".join("?" for _ in pids)
    sql = f"""
        SELECT patient_id, data_hash
        FROM PatientKeywords
        WHERE patient_id IN ({placeholders});
    """
    df = pd.read_sql(sql, conn, params=pids)
    return dict(zip(df["patient_id"], df["data_hash"]))

def upsert_results(
    conn: pyodbc.Connection,
    results: Dict[str, Any],
    new_hashes: Dict[str, str],
):
    """
    Insert or update patient keyword results in the database.

    Uses SQL MERGE statement to efficiently insert new records or update
    existing ones. Stores keywords and reasoning as JSON, along with the
    data hash for future change detection. Uses fast_executemany for
    optimal batch performance.

    Args:
        conn (pyodbc.Connection): Active database connection
        results (Dict[str, Any]): Dictionary mapping patient IDs to their
            keyword results (containing 'keywords' and 'reasoning' keys)
        new_hashes (Dict[str, str]): Dictionary mapping patient IDs to their
            new data hashes

    Raises:
        pyodbc.Error: If database operation fails
        KeyError: If patient ID is missing from new_hashes

    Example:
        >>> results = {
        ...     "12345": {
        ...         "keywords": ["diabetes", "hypertension"],
        ...         "reasoning": {"diabetes": "Patient has Type 2 DM"}
        ...     }
        ... }
        >>> hashes = {"12345": "abc123def456"}
        >>> with sql_connection() as conn:
        ...     upsert_results(conn, results, hashes)
    """
    logger.info(f"Upserting {len(results)} patient results to database.")

    # Prepare batch data for SQL MERGE operation
    batch_rows: List[Tuple[str, str, str, str]] = []
    for pid, pdata in results.items():
        # Serialize keywords and reasoning as JSON
        kws_json = json.dumps(pdata.get("keywords", []))
        rea_json = json.dumps(pdata.get("reasoning", {}))
        data_hash = new_hashes[pid]
        batch_rows.append((pid, kws_json, rea_json, data_hash))

    # Execute batch upsert using MERGE statement
    with conn.cursor() as cur:
        cur.fast_executemany = True  # Enable fast batch execution
        cur.executemany(MERGE_SQL, batch_rows)
        conn.commit()

    logger.info("Upsert complete.")