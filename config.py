"""
Centralized configuration for keywordsV1 project.
All configurable values are set here, not in .env.
"""

# API Keys for LLM services
GEMINI_API_KEY = "AIzaSyCeRpmqPkfPKVPPV4CuQHyJY4JmtW5pyxk"  # Google Gemini API key

# Database connection settings
DB_DRIVER = "ODBC Driver 18 for SQL Server"  # SQL Server ODBC driver
DB_SERVER = "HAARLT0397\SQLEXPRESS"          # SQL Server instance
DB_NAME = "Visa_Data_05262025"               # Database name
DB_ENCRYPT = "no"                            # Encryption setting
DB_TRUST_CERT = "yes"                        # Trust server certificate

# Runtime limits and processing parameters
MAX_TOKENS_PER_RUN = 900_000    # Maximum tokens per LLM call to prevent excessive costs
MAX_COST_USD = 5                # Maximum cost per run in USD
BATCH_SIZE = 25                 # Number of patients to process in each batch
LLM_TEMPERATURE = 0.2           # LLM temperature for consistent outputs (lower = more deterministic)
LLM_RETRY_ATTEMPTS = 4          # Number of retry attempts for failed LLM calls

# Pricing configuration (USD per 1K tokens)
GEMINI_PRICING = {"input": 0.00025, "output": 0.0005}  # Gemini 1.5 Flash pricing

# Data filtering options
APPOINTMENT_START_DATE = '2024-06-26'  # Only process patients with appointments after this date (optional)

# Input data configuration
CAMPAIGN_KEYWORDS_CSV = "data/CampaignKeywords.csv"  # Path to campaign keywords CSV file

# Logging configuration
LOG_LEVEL = "INFO"              # Logging level (DEBUG, INFO, WARNING, ERROR)
LOG_FILE = "pipeline.log"       # Log file path for persistent logging

# Configuration validation
def validate_config():
    """
    Validate that all required configuration values are present.

    Checks for the presence of critical configuration values needed
    for the pipeline to function properly. Raises an error if any
    required values are missing.

    Raises:
        EnvironmentError: If any required configuration values are missing.

    Example:
        >>> validate_config()  # Raises error if GEMINI_API_KEY is empty
    """
    missing = []
    if not GEMINI_API_KEY:
        missing.append("GEMINI_API_KEY")
    if not DB_SERVER:
        missing.append("DB_SERVER")
    if missing:
        raise EnvironmentError(f"Missing required config: {', '.join(missing)}")