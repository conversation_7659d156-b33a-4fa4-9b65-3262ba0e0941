"""
Main pipeline module for processing patient data and generating campaign keywords.

This module orchestrates the entire patient keyword extraction pipeline:
1. Loads campaign keywords from CSV
2. Fetches patient data from database in batches
3. Uses hash-based change detection to process only modified patients
4. Generates keywords using LLM analysis
5. Stores results back to the database

The pipeline is designed to be resumable and efficient, processing only
patients whose data has changed since the last run.
"""

import logging
import json
import config
from src import db
from src import llm
from utils import hashing
from utils import validation
import os

# Setup logging to both file and console
logging.basicConfig(
    level=config.LOG_LEVEL,
    format="%(asctime)s | %(levelname)s | %(name)s | %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(config.LOG_FILE, mode="a", encoding="utf-8")
    ]
)
logger = logging.getLogger("patient_pipeline")

logger.info(f"Logging initialized. Writing to {os.path.abspath(config.LOG_FILE)}")

def process_batch(
    patient_json,
    campaign,
):
    """
    Process a batch of patients through Gemini LLM to generate campaign keywords.

    This function takes a batch of patient data and generates campaign keywords
    for each patient using Google's Gemini LLM service. It includes token counting,
    cost estimation, and response validation.

    Args:
        patient_json (Dict[str, Any]): Dictionary mapping patient IDs to their clinical data
        campaign (List[str]): List of valid campaign keywords to choose from

    Returns:
        Dict[str, Any]: Parsed and validated LLM response containing keywords and reasoning
        for each patient

    Raises:
        RuntimeError: If token or cost limits are exceeded
        Exception: If LLM call fails or response validation fails

    Example:
        >>> patient_data = {"123": {"Results": [...], "Problems": [...]}}
        >>> keywords = ["diabetes", "hypertension", "asthma"]
        >>> result = process_batch(patient_data, keywords)
    """
    logger.info(f"Processing batch with {len(patient_json)} patients using Gemini LLM")

    # Build the prompt with patient data and campaign keywords
    prompt = llm.build_prompt(patient_json, campaign)

    # Count tokens and check limits before making expensive LLM call
    in_tokens = hashing.count_tokens(prompt)
    llm.check_run_limits(in_tokens, out_tok_est=8_000)

    # Make the Gemini LLM call
    logger.info("Calling Gemini LLM...")
    raw_response = llm.query_gemini(prompt)

    # Parse and validate the response
    logger.info("LLM call complete. Parsing response...")
    parsed = validation.validate_and_parse(raw_response, campaign)
    logger.info("Batch processed and validated.")

    return parsed

def main():
    """
    Main pipeline function that orchestrates the entire keyword extraction process.

    This function implements the core pipeline logic:
    1. Loads campaign keywords from CSV file
    2. Processes patients in batches with pagination
    3. Uses hash-based change detection to skip unchanged patients
    4. Generates keywords using LLM for changed patients only
    5. Stores results in the database

    The pipeline is designed to be resumable - it can be stopped and restarted
    without losing progress, as it only processes patients whose data has changed.

    Raises:
        FileNotFoundError: If campaign keywords CSV file is not found
        Exception: If database connection or LLM processing fails

    Example:
        >>> main()  # Processes all patients in the database
    """
    logger.info("🚀 Starting Patient Keyword Pipeline – hash-aware mode")

    # Load campaign keywords from CSV file
    csv_file = config.CAMPAIGN_KEYWORDS_CSV
    with open(csv_file, newline="", encoding="utf-8") as f:
        import csv
        rdr = csv.reader(f)
        next(rdr, None)  # skip header
        campaign_kw = [cell.strip() for row in rdr for cell in row if cell.strip()]
    logger.info("Loaded %d campaign keywords.", len(campaign_kw))

    # Initialize pagination and tracking variables
    offset = 0
    total_processed = 0
    # Main processing loop - continues until all patients are processed
    while True:
        # Fetch the next batch of patient IDs
        with db.sql_connection() as conn:
            db.ensure_tables(conn)  # Ensure database tables exist
            patient_ids = db.fetch_patient_ids(conn, offset, batch=config.BATCH_SIZE)

        # Exit loop if no more patients to process
        if not patient_ids:
            logger.info("No more patients. Exiting loop.")
            break

        # Fetch detailed patient data and existing hashes for change detection
        with db.sql_connection() as conn:
            patient_data = db.fetch_batch_patient_data(conn, patient_ids)
            from utils.hashing import canonicalize_patient_data, stable_hash
            old_hashes = db.get_existing_hashes(conn, list(patient_data.keys()))

        # Identify patients whose data has changed since last processing
        changed_data = {}
        new_hashes = {}
        for pid, pdata in patient_data.items():
            # Create canonical representation and compute hash
            canon = canonicalize_patient_data(pdata)
            h = stable_hash(canon)

            # Only include patients whose data has changed
            if h != old_hashes.get(pid):
                changed_data[pid] = pdata
                new_hashes[pid] = h

        # Skip this batch if no changes detected
        if not changed_data:
            logger.info("Offset %d: no changes detected, skipping.", offset)
            offset += config.BATCH_SIZE
            continue

        # Process changed patients through LLM
        try:
            results = process_batch(changed_data, campaign_kw)
        except Exception as e:
            logger.error("LLM processing failed at offset %d: %s", offset, e)
            offset += config.BATCH_SIZE
            continue

        # Store results in database
        with db.sql_connection() as conn:
            db.upsert_results(conn, results, new_hashes)

        # Update progress tracking
        total_processed += len(changed_data)
        logger.info("Processed %d changed patients (cumulative %d).", len(changed_data), total_processed)
        offset += config.BATCH_SIZE