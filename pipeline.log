2025-06-27 12:26:57,672 | INFO | patient_pipeline | Logging initialized. Writing to C:\Users\<USER>\Documents\CampaignKeywords\keywordsV1\pipeline.log
2025-06-27 12:26:57,673 | INFO | patient_pipeline | 🚀 Starting Patient Keyword Pipeline – hash-aware mode
2025-06-27 12:26:57,674 | INFO | patient_pipeline | Loaded 180 campaign keywords.
2025-06-27 12:26:57,969 | INFO | patient_pipeline | Fetching patient IDs from Appointment: offset=0, batch=25
2025-06-27 12:26:58,343 | INFO | patient_pipeline | Fetched 6 patient IDs.
2025-06-27 12:26:58,396 | WARNING | patient_pipeline | No clinical data found for patient_id=1 (from Appointment table). Skipping.
2025-06-27 12:26:58,396 | INFO | patient_pipeline | Patient 1 has no data and will be skipped from processing batch.
2025-06-27 12:27:00,113 | INFO | patient_pipeline | Offset 0: no changes detected, skipping.
2025-06-27 12:27:00,114 | INFO | patient_pipeline | Fetching patient IDs from Appointment: offset=25, batch=25
2025-06-27 12:27:00,117 | INFO | patient_pipeline | Fetched 0 patient IDs.
2025-06-27 12:27:00,118 | INFO | patient_pipeline | No more patients. Exiting loop.
2025-06-27 12:28:13,796 | INFO | patient_pipeline | Logging initialized. Writing to C:\Users\<USER>\Documents\CampaignKeywords\keywordsV1\pipeline.log
2025-06-27 12:28:13,797 | INFO | patient_pipeline | 🚀 Starting Patient Keyword Pipeline – hash-aware mode
2025-06-27 12:28:13,798 | INFO | patient_pipeline | Loaded 180 campaign keywords.
2025-06-27 12:28:14,058 | INFO | patient_pipeline | Fetching patient IDs from Appointment: offset=0, batch=25
2025-06-27 12:28:14,362 | INFO | patient_pipeline | Fetched 6 patient IDs.
2025-06-27 12:28:14,399 | WARNING | patient_pipeline | No clinical data found for patient_id=1 (from Appointment table). Skipping.
2025-06-27 12:28:14,399 | INFO | patient_pipeline | Patient 1 has no data and will be skipped from processing batch.
2025-06-27 12:28:16,112 | INFO | patient_pipeline | Offset 0: no changes detected, skipping.
2025-06-27 12:28:16,113 | INFO | patient_pipeline | Fetching patient IDs from Appointment: offset=25, batch=25
2025-06-27 12:28:16,116 | INFO | patient_pipeline | Fetched 0 patient IDs.
2025-06-27 12:28:16,117 | INFO | patient_pipeline | No more patients. Exiting loop.
2025-06-27 12:48:43,308 | INFO | patient_pipeline | Logging initialized. Writing to C:\Users\<USER>\Documents\CampaignKeywords\keywordsV1\pipeline.log
2025-06-27 12:48:43,308 | INFO | patient_pipeline | 🚀 Starting Patient Keyword Pipeline – hash-aware mode
2025-06-27 12:48:43,313 | INFO | patient_pipeline | Loaded 180 campaign keywords.
2025-06-27 12:48:43,765 | INFO | patient_pipeline | Fetching patient IDs from Appointment: offset=0, batch=25
2025-06-27 12:48:44,537 | INFO | patient_pipeline | Fetched 6 patient IDs.
2025-06-27 12:48:44,572 | WARNING | patient_pipeline | No clinical data found for patient_id=1 (from Appointment table). Skipping.
2025-06-27 12:48:44,573 | INFO | patient_pipeline | Patient 1 has no data and will be skipped from processing batch.
2025-06-27 12:48:50,373 | INFO | patient_pipeline | Offset 0: no changes detected, skipping.
2025-06-27 12:48:50,401 | INFO | patient_pipeline | Fetching patient IDs from Appointment: offset=25, batch=25
2025-06-27 12:48:50,409 | INFO | patient_pipeline | Fetched 0 patient IDs.
2025-06-27 12:48:50,409 | INFO | patient_pipeline | No more patients. Exiting loop.
2025-06-27 16:14:32,721 | INFO | patient_pipeline | Logging initialized. Writing to C:\Users\<USER>\Documents\CampaignKeywords\keywordsV1\pipeline.log
2025-06-27 16:14:32,722 | INFO | patient_pipeline | 🚀 Starting Patient Keyword Pipeline – hash-aware mode
2025-06-27 16:14:32,726 | INFO | patient_pipeline | Loaded 180 campaign keywords.
2025-06-27 16:14:33,092 | INFO | patient_pipeline | Fetching patient IDs from Appointment: offset=0, batch=25
2025-06-27 16:14:33,868 | INFO | patient_pipeline | Fetched 6 patient IDs.
2025-06-27 16:14:33,896 | WARNING | patient_pipeline | No clinical data found for patient_id=1 (from Appointment table). Skipping.
2025-06-27 16:14:33,897 | INFO | patient_pipeline | Patient 1 has no data and will be skipped from processing batch.
2025-06-27 16:14:35,532 | INFO | patient_pipeline | Processing batch with 1 patients using Gemini LLM
2025-06-27 16:14:35,532 | INFO | patient_pipeline | Building prompt for 1 patients and 180 campaign keywords
2025-06-27 16:14:35,684 | INFO | patient_pipeline | Calling Gemini LLM...
2025-06-27 16:14:45,890 | INFO | patient_pipeline | LLM call complete. Parsing response...
2025-06-27 16:14:45,891 | WARNING | patient_pipeline | Patient 11: expected 7 valid keywords after filtering, got 6
2025-06-27 16:14:45,891 | WARNING | patient_pipeline | Found 1 invalid keywords in batch: [('11', ['dizziness'])]
2025-06-27 16:14:45,892 | INFO | patient_pipeline | Batch processed and validated.
2025-06-27 16:14:45,901 | INFO | patient_pipeline | Upserting 1 patient results to database.
2025-06-27 16:14:45,913 | INFO | patient_pipeline | Upsert complete.
2025-06-27 16:14:45,914 | INFO | patient_pipeline | Processed 1 changed patients (cumulative 1).
2025-06-27 16:14:45,914 | INFO | patient_pipeline | Fetching patient IDs from Appointment: offset=25, batch=25
2025-06-27 16:14:45,918 | INFO | patient_pipeline | Fetched 0 patient IDs.
2025-06-27 16:14:45,918 | INFO | patient_pipeline | No more patients. Exiting loop.
